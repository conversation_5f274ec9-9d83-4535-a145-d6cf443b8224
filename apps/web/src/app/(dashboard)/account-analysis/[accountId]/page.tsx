import { AccountTweetsFilter } from '@/components/account-tweets-filter';

interface PageProps {
  params: {
    accountId: string;
  };
}

export default function AccountAnalysisPage({ params }: PageProps) {
  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">Account Analysis</h1>
      <AccountTweetsFilter accountId={params.accountId} />
    </div>
  );
}